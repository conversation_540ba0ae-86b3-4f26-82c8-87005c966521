import { Injectable } from '@nestjs/common';
import { eq, sql } from 'drizzle-orm';

import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { InsertablePage, Page, pages, UpdatablePage } from '../../../../drizzle/schema';

@Injectable()
export class PageRepo {
    constructor(private readonly db: DrizzleDB) {}

    async findById(pageId: string): Promise<Page | undefined> {
        const result = await this.db.select().from(pages).where(eq(pages.id, pageId)).limit(1);
        return result[0];
    }

    async updatePage(updatablePage: UpdatablePage, pageId: string, trx?: DrizzleTransaction): Promise<void> {
        return this.updatePages(updatablePage, [pageId], trx);
    }

    async updatePages(updatePageData: UpdatablePage, pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);

        const now = new Date();

        await db
            .update(pages)
            .set({ ...updatePageData, updatedAt: now })
            .where(sql`${pages.id} IN (${sql.join(pageIds.map((id) => sql`${id}`))})`)
            .execute();
    }

    async insertPage(insertablePage: InsertablePage, trx?: DrizzleTransaction): Promise<Page | undefined> {
        const result = await this.db.insert(pages).values(insertablePage).returning().execute();
        return result?.[0];
    }

    async deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(pages).where(eq(pages.id, pageId)).execute();
    }

    async exists(pageId: string): Promise<boolean> {
        const result = await this.db
            .select({ id: pages.id })
            .from(pages)
            .where(eq(pages.id, pageId))
            .limit(1)
            .execute();

        return result.length > 0;
    }
}
