import { Injectable, OnM<PERSON>uleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';

import { appConfig } from '../configs/app.config';
import { DrizzleDB } from './drizzle.types';

@Injectable()
export class DrizzleService implements OnModuleInit, OnModuleDestroy {
    private pool: Pool;
    public readonly db: DrizzleDB;

    constructor() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
        this.pool = new Pool({ connectionString: appConfig.dbUrl });
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        this.db = drizzle(this.pool);
    }

    // по желанию можно проверять соединение
    async onModuleInit() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        await this.pool.connect();
        console.log('✅ DrizzleService connected to Postgres');
    }

    async onModuleDestroy() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        await this.pool.end();
        console.log('🛑 Postgres pool has ended');
    }
}
