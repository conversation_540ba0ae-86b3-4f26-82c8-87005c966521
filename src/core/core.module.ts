import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from '@skillspace/access';
import { ExecutionContextRouterModule } from '@skillspace/common';
import {
    ApolloFederationDriver,
    ApolloFederationDriverConfig,
    ApolloServerPluginInlineTraceDisabled,
    GqlInterceptor,
    GraphqlExceptionFilter,
    GraphQLModule,
} from '@skillspace/graphql';
import { GrpcModule } from '@skillspace/grpc';
import { LoggerModule } from '@skillspace/logger';
import { OpentelemetryModule } from '@skillspace/tracing';

import { appConfig, isProd, isTest } from '../configs/app.config';
import { DrizzleModule } from '../drizzle/drizzle.module';

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [() => ({ app: appConfig })],
        }),
        EventEmitterModule.forRoot(),
        CqrsModule,
        ScheduleModule.forRoot(),
        AuthModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>('JWT_SECRET'),
            }),
        }),
        GraphQLModule.forRoot<ApolloFederationDriverConfig>({
            driver: ApolloFederationDriver,
            playground: !isProd,
            autoSchemaFile: true,
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                };
            },
            plugins: isTest ? [ApolloServerPluginInlineTraceDisabled()] : [],
        }),
        LoggerModule.forRoot(),
        OpentelemetryModule.forRoot(),
        ExecutionContextRouterModule.register({
            gqlInterceptor: GqlInterceptor,
            gqlFilter: GraphqlExceptionFilter,
        }),
        GrpcModule.register({
            packageName: 'editor',
            address: appConfig.editorGrpcAddress,
        }),
        DrizzleModule,
    ],
    providers: [
        // Temporarily disable auth guards for testing
        // {
        //     provide: APP_GUARD,
        //     useClass: JwtAuthGqlGuard,
        // },
        // {
        //     provide: APP_GUARD,
        //     useClass: PermissionsGuard,
        // },
        Reflector,
    ],
    exports: [AuthModule],
})
export class CoreModule {}
