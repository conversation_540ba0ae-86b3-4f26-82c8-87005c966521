import { INestApplication } from '@nestjs/common';
import { AuthService, DecodeJwtAuth } from '@skillspace/access';
import { DocumentNode, print } from 'graphql';
import { gql } from 'graphql-tag';
import { Server } from 'http';
import * as request from 'supertest';
import TestAgent from 'supertest/lib/agent.js';

import { CreatePageInput } from '../src/modules/editor/presentation/graphql/page.inputs';
import { PageType } from '../src/modules/editor/presentation/graphql/page.types';

type JSONPrimitive = string | number | boolean | null;
type JSONArray = JSONValue[];
type JSONValue = JSONPrimitive | JSONObject | JSONArray;

export interface JSONObject {
    [member: string]: JSONValue;
}

export class GqlTestSession {
    private agent: TestAgent;

    constructor(
        private readonly server: Server,
        private readonly token: string,
    ) {
        this.agent = request(this.server);
    }

    public async gql<T>(query: DocumentNode, variables: Record<string, any> = {}): Promise<T> {
        const queryString = print(query);

        const response = await this.agent
            .post('/graphql')
            .set('Authorization', `Bearer ${this.token}`)
            .set('Content-Type', 'application/json')
            .send({ query: queryString, variables });

        if (response.body.errors) {
            throw new Error(`GraphQL Error: ${JSON.stringify(response.body.errors)}`);
        }

        return response.body.data as T;
    }

    // public async getWorkspaceTariffs(
    //     workspaceId: string,
    //     isPublic: boolean,
    // ): Promise<{ workspace: WorkspaceOutput } | null> {
    //     const query = gql`
    //         query {
    //             workspace(workspaceId: "${workspaceId}", isPublic: ${isPublic}) {
    //                 ${workspaceFields}
    //             }
    //         }
    //     `;
    //     return this.gql<{ workspace: WorkspaceOutput }>(query);
    // }

    public async createPage(input: CreatePageInput): Promise<PageType> {
        const CREATE_PAGE_MUTATION = gql`
            mutation CreatePage($input: CreatePageInput!) {
                createPage(input: $input) {
                    id
                    serviceId
                    entityId
                    schoolId
                    createdAt
                    updatedAt
                }
            }
        `;

        const response = await this.gql<{ createPage: PageType }>(CREATE_PAGE_MUTATION, { input });
        return response.createPage;
    }

    public async getPage(id: string): Promise<PageType> {
        const GET_PAGE_QUERY = gql`
            query GetPage($id: ID!) {
                page(id: $id) {
                    id
                    content
                    textContent
                    isLocked
                    serviceId
                    entityId
                    schoolId
                    createdAt
                    updatedAt
                }
            }
        `;

        const response = await this.gql<{ page: PageType }>(GET_PAGE_QUERY, { id });
        return response.page;
    }

    public async deletePage(id: string): Promise<boolean> {
        const DELETE_PAGE_MUTATION = gql`
            mutation DeletePage($id: ID!) {
                deletePage(id: $id)
            }
        `;

        const response = await this.gql<{ deletePage: boolean }>(DELETE_PAGE_MUTATION, { id });
        return response.deletePage;
    }

    // Методы для тестирования валидации и edge cases
    public async createPageWithValidation(input: any): Promise<PageType> {
        const CREATE_PAGE_MUTATION = gql`
            mutation CreatePage($input: CreatePageInput!) {
                createPage(input: $input) {
                    id
                    content
                    textContent
                    isLocked
                    serviceId
                    entityId
                    schoolId
                    createdAt
                    updatedAt
                }
            }
        `;

        const response = await this.gql<{ createPage: PageType }>(CREATE_PAGE_MUTATION, { input });
        return response.createPage;
    }

    public async expectGraphQLError(operation: () => Promise<any>): Promise<string> {
        try {
            await operation();
            throw new Error('Expected GraphQL error but operation succeeded');
        } catch (error) {
            return error.message;
        }
    }
}

export async function createGqlTestSession(app: INestApplication, context: DecodeJwtAuth): Promise<GqlTestSession> {
    const authService = app.get<AuthService>(AuthService);
    const token = await authService.signAuthorizationToken(context);

    // const userToken = await authService.signAuthorizationToken({
    //     userId: '4a22c9e5-67f5-44fb-aabc-afb61cf592b6',
    //     schoolId: '3479a48b-bd9b-4d79-be83-2960d0dbe7ec',
    //     role: 'ROLE_EMPLOYEE' as const,
    //     actions: [],
    //     userName: 'Спиридон Никанорович',
    //     userEmail: '<EMAIL>',
    //     unionAuthKey: '24',
    // });
    // console.log({ userToken });
    const server: Server = app.getHttpServer();
    return new GqlTestSession(server, token);
}
