import { randomUUID } from 'node:crypto';

import { CreatePageInput } from '../../src/modules/editor/presentation/graphql/page.inputs';
import { app, userSession } from './test-setup';

// Вспомогательная функция для создания базового input с обязательными полями
function createBaseInput(overrides: Partial<CreatePageInput> = {}): CreatePageInput {
    return {
        serviceId: 'test-service',
        entityId: 'test-entity',
        schoolId: 'test-school',
        ...overrides,
    };
}

describe('PageResolver Integration Tests', () => {
    beforeAll(async () => {
        // Убедимся, что приложение инициализировано
        expect(app).toBeDefined();
        expect(userSession).toBeDefined();
    });

    describe('createPage mutation', () => {
        it('должен создать страницу через GraphQL', async () => {
            const input: CreatePageInput = {
                serviceId: 'graphql-service',
                entityId: 'graphql-entity',
                schoolId: 'graphql-school',
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.id).toBeDefined();
            expect(page.serviceId).toBe('graphql-service');
            expect(page.entityId).toBe('graphql-entity');
            expect(page.schoolId).toBe('graphql-school');
            // expect(page.content).toBeNull();
            // expect(page.textContent).toBeNull();
        });

        it('должен создать страницу с минимальными данными', async () => {
            const input: CreatePageInput = {
                serviceId: 'minimal-service',
                entityId: 'minimal-entity',
                schoolId: 'minimal-school',
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.id).toBeDefined();
            expect(page.serviceId).toBe('minimal-service');
            expect(page.entityId).toBe('minimal-entity');
            expect(page.schoolId).toBe('minimal-school');
        });

        it('должен создать страницу с различными serviceId', async () => {
            const input: CreatePageInput = {
                serviceId: 'different-service',
                entityId: 'different-entity',
                schoolId: 'different-school',
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.id).toBeDefined();
            expect(page.serviceId).toBe('different-service');
            expect(page.entityId).toBe('different-entity');
            expect(page.schoolId).toBe('different-school');
        });

        it('должен создать страницы с уникальными entityId', async () => {
            const input1: CreatePageInput = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-1',
                schoolId: 'unique-school',
            };

            const input2: CreatePageInput = {
                serviceId: 'unique-service',
                entityId: 'unique-entity-2',
                schoolId: 'unique-school',
            };

            const page1 = await userSession.createPage(input1);
            const page2 = await userSession.createPage(input2);

            expect(page1).toBeDefined();
            expect(page2).toBeDefined();
            expect(page1.id).not.toBe(page2.id);
            expect(page1.entityId).toBe('unique-entity-1');
            expect(page2.entityId).toBe('unique-entity-2');
        });
    });

    describe('page query', () => {
        it('должен получить страницу по ID через GraphQL', async () => {
            // Создаем страницу
            const input = createBaseInput({
                entityId: 'query-entity',
            });

            const createdPage = await userSession.createPage(input);

            // Получаем страницу
            const page = await userSession.getPage(createdPage.id);

            expect(page).toBeDefined();
            expect(page.id).toBe(createdPage.id);
            expect(page.serviceId).toBe('test-service');
            expect(page.entityId).toBe('query-entity');
            expect(page.schoolId).toBe('test-school');
            expect(page.isLocked).toBe(false);
            expect(page.content).toBeNull();
            expect(page.textContent).toBeNull();
        });

        it('должен вернуть ошибку для несуществующей страницы', async () => {
            const nonExistentId = randomUUID();

            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.getPage(nonExistentId);
            });

            expect(errorMessage).toContain(`Page with ID ${nonExistentId} not found`);
        });
    });

    describe('deletePage mutation', () => {
        it('должен удалить страницу через GraphQL', async () => {
            // Создаем страницу
            const input = createBaseInput({
                entityId: 'delete-entity',
            });

            const createdPage = await userSession.createPage(input);

            // Проверяем, что страница существует
            const page = await userSession.getPage(createdPage.id);
            expect(page).toBeDefined();

            // Удаляем страницу
            const deleteResult = await userSession.deletePage(createdPage.id);
            expect(deleteResult).toBe(true);

            // Проверяем, что страница больше не существует
            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.getPage(createdPage.id);
            });

            expect(errorMessage).toContain('not found');
        });

        it('должен вернуть ошибку при попытке удалить несуществующую страницу', async () => {
            const nonExistentId = randomUUID();

            const errorMessage = await userSession.expectGraphQLError(async () => {
                await userSession.deletePage(nonExistentId);
            });

            expect(errorMessage).toContain(`Page with ID ${nonExistentId} not found`);
        });
    });

    describe('input validation', () => {
        it('должен принять валидные строки для обязательных полей', async () => {
            const input: CreatePageInput = {
                serviceId: 'valid-service-123',
                entityId: 'valid-entity-456',
                schoolId: 'valid-school-789',
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('valid-service-123');
            expect(page.entityId).toBe('valid-entity-456');
            expect(page.schoolId).toBe('valid-school-789');
        });

        it('должен обработать специальные символы в полях', async () => {
            const input: CreatePageInput = {
                serviceId: 'service-with-special-chars-!@#$%',
                entityId: 'entity_with_underscores_and_numbers_123',
                schoolId: 'school-with-émojis-🏫',
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe('service-with-special-chars-!@#$%');
            expect(page.entityId).toBe('entity_with_underscores_and_numbers_123');
            expect(page.schoolId).toBe('school-with-émojis-🏫');
        });

        it('должен обработать длинные строки', async () => {
            const longServiceId = 'service-' + 'A'.repeat(100);
            const longEntityId = 'entity-' + 'B'.repeat(100);
            const longSchoolId = 'school-' + 'C'.repeat(100);

            const input: CreatePageInput = {
                serviceId: longServiceId,
                entityId: longEntityId,
                schoolId: longSchoolId,
            };

            const page = await userSession.createPage(input);

            expect(page).toBeDefined();
            expect(page.serviceId).toBe(longServiceId);
            expect(page.entityId).toBe(longEntityId);
            expect(page.schoolId).toBe(longSchoolId);
        });
    });
});
