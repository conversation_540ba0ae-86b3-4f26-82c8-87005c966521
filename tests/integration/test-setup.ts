import 'reflect-metadata';

import { INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { createLogger } from '@skillspace/logger';

import { AppModule } from '../../src/app.module.js';
import { setupGlobalMiddlewares } from '../../src/bootstrap-setup.js';
import { DrizzleService } from '../../src/drizzle/drizzle.service.js';
import { DrizzleDB } from '../../src/drizzle/drizzle.types.js';
import { PageService } from '../../src/modules/editor/application/services/page.service.js';
import { PageResolver } from '../../src/modules/editor/presentation/graphql/page.resolver.js';
import { createGqlTestSession, GqlTestSession } from '../user-session.js';
import { PLANS_USER } from './test-user.js';

let app: INestApplication;
let testDb: DrizzleDB;

let pageService: PageService;
let pageResolver: PageResolver;

let userSession: GqlTestSession;

beforeAll(async () => {
    const builder = Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleFixture = await builder.compile();
    app = moduleFixture.createNestApplication({ logger: await createLogger() });

    setupGlobalMiddlewares(app);

    await app.init();

    pageService = app.get<PageService>(PageService);
    pageResolver = app.get<PageResolver>(PageResolver);

    userSession = await createGqlTestSession(app, PLANS_USER);
    testDb = app.get<DrizzleService>(DrizzleService).db;
}, 30000);

beforeEach(() => {
    jest.clearAllMocks();
});

afterAll(async () => {
    await app.close();
});

export { app, pageService, pageResolver, testDb, userSession };
