module.exports = {
    moduleFileExtensions: ['js', 'json', 'ts'],
    rootDir: '../',
    modulePaths: ['.'],
    testRegex: '.*\\.test\\.ts$',
    transform: {
        '^.+\\.(t|j)s$': 'ts-jest',
    },
    collectCoverageFrom: ['<rootDir>/**/*.(t|j)s'],
    coveragePathIgnorePatterns: ['<rootDir>/tests'],
    coverageDirectory: '<rootDir>/tests/coverage',
    globalSetup: '<rootDir>/tests/config/jest.setup.cjs',
    globalTeardown: '<rootDir>/tests/config/jest.teardown.cjs',
    setupFiles: ['<rootDir>/tests/config/jest.otel-mock.cjs'],
    setupFilesAfterEnv: ['<rootDir>/tests/config/jest.setupAfterEnv.cjs'],
    workerIdleMemoryLimit: 0.2,
    watchPathIgnorePatterns: ['!<rootDir>/src/', '!<rootDir>/tests/', '<rootDir>/.data/'],
    moduleNameMapper: {
        '^(\\.{1,2}/.*)\\.js$': '$1',
    },
    transformIgnorePatterns: ['node_modules/(?!(@tiptap|@hocuspocus|yjs)/)'],
    preset: 'ts-jest',
    testEnvironment: 'node',
    testTimeout: 60000, // Increased for WebSocket tests
};
