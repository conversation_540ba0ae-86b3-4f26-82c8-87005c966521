module.exports = async () => {
    //                                   # bytes |  KB  | MB   | GB
    const gbNow = process.memoryUsage().heapUsed / 1024 / 1024 / 1024;
    const gbRounded = Math.round(gbNow * 100) / 100;
    console.log(`<PERSON>ap allocated ${gbRounded} GB`);

    // Очищаем все таймеры
    const timers = require('timers');
    if (timers.clearInterval) {
        // Очищаем все активные интервалы
        for (let i = 1; i < 10000; i++) {
            try {
                clearInterval(i);
                clearTimeout(i);
            } catch (e) {
                // Игнорируем ошибки
            }
        }
    }

    // Останавливаем контейнеры
    if (global.__DATABASE_CONTAINER__) {
        await global.__DATABASE_CONTAINER__.stop();
    }

    if (global.__REDIS_CONTAINER__) {
        await global.__REDIS_CONTAINER__.stop();
    }

    // Принудительно завершаем процесс
    setTimeout(() => {
        process.exit(0);
    }, 100);
};
