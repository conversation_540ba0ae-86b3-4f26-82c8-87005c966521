# WebSocket Testing Guide

Руководство по тестированию WebSocket соединений и совместного редактирования документов.

## Автоматические тесты

### Интеграционные тесты WebSocket

Тесты находятся в файле `tests/integration/collaboration.websocket.test.ts` и проверяют:

- ✅ Установку WebSocket соединения
- ✅ Загрузку существующих документов
- ✅ Синхронизацию изменений между клиентами
- ✅ Сохранение изменений в базе данных
- ✅ Обработку ошибок

### Запуск тестов

```bash
# Все интеграционные тесты
pnpm test:integration

# Только WebSocket тесты
pnpm test:ws

# Конкретный тест файл
pnpm test -- collaboration.websocket.test.ts
```

### Особенности тестирования

1. **HTTP сервер**: Тесты запускают полноценный HTTP сервер для WebSocket соединений
2. **Реальные соединения**: Используются настоящие WebSocket соединения
3. **База данных**: Тесты работают с тестовой базой данных
4. **Изоляция**: Каждый тест изолирован и очищает данные

## Ручное тестирование

### Запуск тестового клиента

1. **Запустите основное приложение:**
   ```bash
   pnpm start:dev
   ```

2. **Запустите тестовый клиент:**
   ```bash
   pnpm client
   ```

3. **Откройте браузер:**
   Перейдите по адресу http://localhost:8080

### Тестовые сценарии

#### Сценарий 1: Создание и редактирование документа

1. Откройте тестовый клиент
2. Нажмите "Create New Page"
3. Введите название "Test Document"
4. Нажмите "Connect"
5. Используйте "Insert Sample Text" для добавления контента
6. Проверьте, что изменения отображаются в редакторе

#### Сценарий 2: Совместное редактирование

1. Откройте две вкладки с тестовым клиентом
2. В первой вкладке создайте документ
3. Во второй вкладке загрузите список страниц и выберите созданный документ
4. Подключитесь к документу в обеих вкладках
5. Внесите изменения в первой вкладке
6. Убедитесь, что изменения появились во второй вкладке

#### Сценарий 3: Восстановление после разрыва соединения

1. Подключитесь к документу
2. Внесите изменения
3. Отключитесь от сети или остановите сервер
4. Восстановите соединение
5. Убедитесь, что изменения сохранились

### Проверка логов

Тестовый клиент ведет подробные логи всех операций:
- Подключение/отключение WebSocket
- Получение и отправка сообщений
- Ошибки соединения
- Операции с документами

## Архитектура тестирования

### TestServer

Утилита для запуска HTTP сервера в тестах:

```typescript
const testServer = new TestServer();
const { app, httpServer, port } = await testServer.start();
```

### HocuspocusTestClient

WebSocket клиент для тестирования:

```typescript
const client = new HocuspocusTestClient({
    url: 'http://localhost:3033',
    documentName: 'page.uuid'
});

await client.connect();
client.insertText('Hello World');
const content = client.getContent();
```

### Основные методы клиента

- `connect()` - подключение к WebSocket
- `disconnect()` - отключение
- `updateContent(content)` - обновление содержимого
- `insertText(text)` - вставка текста
- `getContent()` - получение содержимого
- `waitForSync(timeout)` - ожидание синхронизации

## Отладка

### Проблемы с подключением

1. **Проверьте порты:**
   - Основное приложение: http://localhost:3033
   - Тестовый клиент: http://localhost:8080

2. **Проверьте логи сервера:**
   ```bash
   # В терминале с основным приложением
   # Должны появляться сообщения о WebSocket соединениях
   ```

3. **Проверьте браузер:**
   - Откройте DevTools → Network → WS
   - Убедитесь, что WebSocket соединение установлено

### Проблемы с синхронизацией

1. **Проверьте Redis:**
   ```bash
   # Если используется Redis
   redis-cli ping
   ```

2. **Проверьте базу данных:**
   - Убедитесь, что изменения сохраняются в таблице pages
   - Проверьте поле content

3. **Проверьте логи Hocuspocus:**
   - В логах должны быть сообщения о загрузке/сохранении документов

### Типичные ошибки

#### WebSocket connection failed
```
Причина: Сервер не запущен или неправильный URL
Решение: Проверьте, что приложение запущено на правильном порту
```

#### Document not found
```
Причина: Документ с указанным ID не существует
Решение: Создайте документ через GraphQL или тестовый клиент
```

#### Changes not syncing
```
Причина: Проблемы с Y.js или Hocuspocus
Решение: Проверьте логи сервера и клиента
```

## Производительность

### Нагрузочное тестирование

Для тестирования производительности можно:

1. Открыть множество вкладок с клиентом
2. Подключить их к одному документу
3. Одновременно вносить изменения
4. Мониторить использование ресурсов

### Метрики

- Время подключения WebSocket
- Задержка синхронизации изменений
- Использование памяти
- Количество одновременных соединений

## Расширение тестов

### Добавление новых тестов

1. Создайте новый тест файл в `tests/integration/`
2. Используйте `TestServer` для запуска сервера
3. Используйте `HocuspocusTestClient` для WebSocket соединений
4. Добавьте проверки специфичной функциональности

### Пример нового теста

```typescript
it('должен обработать большой документ', async () => {
    const client = new HocuspocusTestClient({
        url: baseUrl,
        documentName: `page.${pageId}`
    });
    
    await client.connect();
    
    // Добавить много контента
    for (let i = 0; i < 1000; i++) {
        client.insertText(`Line ${i}\n`);
    }
    
    await client.waitForSync(5000);
    
    const content = client.getContent();
    expect(content.content.length).toBe(1000);
});
```

## CI/CD интеграция

Тесты WebSocket можно интегрировать в CI/CD:

```yaml
# .github/workflows/test.yml
- name: Run WebSocket tests
  run: |
    pnpm start:dev &
    sleep 10  # Дождаться запуска сервера
    pnpm test:ws
```

Убедитесь, что в CI окружении доступны:
- PostgreSQL
- Redis (если используется)
- Правильные переменные окружения
